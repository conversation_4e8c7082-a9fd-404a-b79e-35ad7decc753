-- Fix missing user columns for subscription and trial tracking
-- Migration: 0006_fix_missing_user_columns.sql

-- Add subscription_status column if it doesn't exist
ALTER TABLE users ADD COLUMN subscription_status TEXT DEFAULT 'active';

-- Add trial tracking columns if they don't exist
ALTER TABLE users ADD COLUMN trial_start_date DATETIME;
ALTER TABLE users ADD COLUMN trial_end_date DATETIME;

-- Update existing users to have proper default subscription status
UPDATE users SET subscription_status = 'active' WHERE subscription_status IS NULL;

-- Create indexes for better performance (now that all columns exist)
CREATE INDEX IF NOT EXISTS idx_users_subscription ON users(subscription_plan, subscription_status);
CREATE INDEX IF NOT EXISTS idx_users_trial_dates ON users(trial_start_date, trial_end_date);
